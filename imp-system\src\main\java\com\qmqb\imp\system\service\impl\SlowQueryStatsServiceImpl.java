package com.qmqb.imp.system.service.impl;


import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzed.structure.common.exception.ServiceException;
import com.qmqb.imp.common.core.domain.entity.SysUser;
import com.qmqb.imp.common.core.domain.model.LoginUser;
import com.qmqb.imp.common.core.page.TableDataInfo;
import com.qmqb.imp.common.enums.PersonTypeEnum;
import com.qmqb.imp.common.helper.LoginHelper;
import com.qmqb.imp.system.domain.DataBasePermission;
import com.qmqb.imp.system.domain.SlowQueryDbList;
import com.qmqb.imp.system.domain.SlowQueryStats;
import com.qmqb.imp.system.domain.vo.SlowSqlCountVO;
import com.qmqb.imp.system.domain.vo.SlowSqlStatVO;
import com.qmqb.imp.system.mapper.SlowQueryDbListMapper;
import com.qmqb.imp.system.mapper.SlowQueryStatsMapper;
import com.qmqb.imp.system.service.ISlowQueryStatsService;
import com.qmqb.imp.system.service.ISysUserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-10 14:29:51
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class SlowQueryStatsServiceImpl extends ServiceImpl<SlowQueryStatsMapper, SlowQueryStats> implements ISlowQueryStatsService {

    private final SlowQueryStatsMapper slowQueryStatsMapper;

    private final SlowQueryDbListMapper slowQueryDbListMapper;

    private final ISysUserService sysUserService;

    @Override
    public List<SlowSqlStatVO> slowSqlStat(Integer year,Integer status) {
        // 权限控制逻辑
        LoginUser loginUser = LoginHelper.getLoginUser();
        if (loginUser == null) {
            throw new ServiceException("当前用户未登录");
        }
        SysUser currentUser = sysUserService.selectUserById(loginUser.getUserId());
        if (currentUser == null) {
            throw new ServiceException("用户信息不存在");
        }

        List<SlowQueryDbList> slowQueryDbLists = slowQueryDbListMapper.selectList();
        // 批量查询所有表的统计数据
        List<SlowSqlCountVO> allCounts = slowQueryStatsMapper.statSlowSqlCount(year,status);
        Map<String, List<SlowSqlCountVO>> countsByTable = allCounts.stream()
            .collect(Collectors.groupingBy(SlowSqlCountVO::getDbName));

        // 构建结果
        List<SlowSqlStatVO> statList = new ArrayList<>();
        slowQueryDbLists.forEach(slowQueryDb -> {
            SlowSqlStatVO vo = new SlowSqlStatVO();
            vo.setDbName(slowQueryDb.getDbName());
            List<Integer> monthCounts = new ArrayList<>(Collections.nCopies(12, 0));
            countsByTable.getOrDefault(slowQueryDb.getDbCode(), Collections.emptyList())
                .forEach(item -> monthCounts.set(item.getMonth() - 1, item.getSlowSqlCount()));
            vo.setMonthOfSlowSqlCount(monthCounts);
            statList.add(vo);
        });

        return statList;
    }
}
