package com.qmqb.imp.web.controller.performance;

import java.util.*;
import java.util.stream.Collectors;

import com.qmqb.imp.common.enums.PersonTypeEnum;
import com.qmqb.imp.job.config.RoleIndicatorConfig;
import com.qmqb.imp.system.domain.bo.performance.PerformIndicatorResultBo;
import com.qmqb.imp.system.domain.vo.performance.IndicatorCategoryVo;
import com.qmqb.imp.system.service.indicator.IPerformIndicatorResultService;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.qmqb.imp.common.annotation.RepeatSubmit;
import com.qmqb.imp.common.annotation.Log;
import com.qmqb.imp.common.core.controller.BaseController;
import com.qmqb.imp.common.core.domain.PageQuery;
import com.qmqb.imp.common.core.domain.R;
import com.qmqb.imp.common.core.validate.AddGroup;
import com.qmqb.imp.common.core.validate.EditGroup;
import com.qmqb.imp.common.enums.BusinessType;
import com.qmqb.imp.system.domain.vo.PerformIndicatorResultVo;

import com.qmqb.imp.common.core.page.TableDataInfo;
import com.qmqb.imp.common.enums.IndicatorCategoryEnum;
import com.qmqb.imp.common.enums.PerformanceIndicatorEnum;

/**
 * 绩效指标原因
 *
 * <AUTHOR>
 * @date 2025-07-01
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/performIndicatorResult")
public class PerformIndicatorResultController extends BaseController {

    private final IPerformIndicatorResultService iPerformIndicatorResultService;

    @Autowired
    private RoleIndicatorConfig roleIndicatorConfig;

    /**
     * 查询绩效指标原因列表
     */
    @GetMapping("/list")
    public TableDataInfo<PerformIndicatorResultVo> list(PerformIndicatorResultBo bo, PageQuery pageQuery) {
        return iPerformIndicatorResultService.queryPageList(bo, pageQuery);
    }


    /**
     * 新增绩效指标原因
     */
    @Log(title = "绩效指标原因", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping("/add")
    public R<Void> add(@Validated(AddGroup.class) @RequestBody PerformIndicatorResultBo bo) {
        return toAjax(iPerformIndicatorResultService.insertByBo(bo));
    }

    /**
     * 修改绩效指标原因
     */
    @Log(title = "绩效指标原因", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("/edit")
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody PerformIndicatorResultBo bo) {
        return toAjax(iPerformIndicatorResultService.updateByBo(bo));
    }

    /**
     * 删除绩效指标原因
     */
    @Log(title = "绩效指标原因", businessType = BusinessType.DELETE)
    @DeleteMapping("/remove/{resultsIds}")
    public R<Void> remove(@PathVariable Long[] resultsIds) {
        iPerformIndicatorResultService.deleteByIds(resultsIds);
        return R.ok();
    }

    /**
     * 一类二类指标下拉数据
     */
    @GetMapping("/indicatorList")
    public R<List<IndicatorCategoryVo>> getIndicatorList(@RequestParam(required = false) String firstIndicatorCode) {
        List<IndicatorCategoryEnum> indicatorCategoryEnums;
        if (StringUtils.isNotBlank(firstIndicatorCode)) {
            IndicatorCategoryEnum indicatorCategory = IndicatorCategoryEnum.getByCode(firstIndicatorCode);
            indicatorCategoryEnums = indicatorCategory != null ? Arrays.asList(indicatorCategory) : new ArrayList<>();
        } else {
            indicatorCategoryEnums = Arrays.asList(IndicatorCategoryEnum.values());
        }

        List<IndicatorCategoryVo> categoryList = indicatorCategoryEnums.stream().map(categoryEnum -> {
            IndicatorCategoryVo categoryVO = new IndicatorCategoryVo();
            categoryVO.setCode(categoryEnum.getCode());
            categoryVO.setName(categoryEnum.getName());
            List<IndicatorCategoryVo.IndicatorVO> children = categoryEnum.getIndicatorCodes().stream()
                .map(PerformanceIndicatorEnum::fromCode)
                .filter(Objects::nonNull)
                .map(indicatorEnum -> {
                    IndicatorCategoryVo.IndicatorVO indicatorVO = new IndicatorCategoryVo.IndicatorVO();
                    indicatorVO.setCode(indicatorEnum.getCode());
                    indicatorVO.setName(indicatorEnum.getName());
                    indicatorVO.setSystemGenerated(indicatorEnum.isSystemGenerated());
                    return indicatorVO;
                })
                .collect(Collectors.toList());
            categoryVO.setSecondaryIndicators(children);
            return categoryVO;
        }).collect(Collectors.toList());

        return R.ok(categoryList);
    }


    /**
     * 获取角色的一类二类指标下拉数据
     * @param roleId
     * @return
     */
    @GetMapping("/getRoleIndicator/{roleId}")
    public R<List<IndicatorCategoryVo>> getRoleIndicator(@PathVariable("roleId") Long roleId) {
        // 如果不是特殊角色，获取角色对应的指标配置
        List<String> indicatorsByRole = PersonTypeEnum.TECHNICAL_MANAGER.getRoleId().equals(roleId) ?
            Collections.emptyList() :
            roleIndicatorConfig.getIndicatorsByRole(PersonTypeEnum.fromRoleId(roleId));
        List<IndicatorCategoryVo> roleIndicatorList = iPerformIndicatorResultService.getRoleIndicator(roleId, indicatorsByRole);
        return R.ok(roleIndicatorList);
    }


}
