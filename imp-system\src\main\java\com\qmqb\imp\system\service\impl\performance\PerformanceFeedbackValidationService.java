package com.qmqb.imp.system.service.impl.performance;

import cn.hutool.core.collection.CollUtil;
import com.hzed.structure.common.exception.ServiceException;
import com.qmqb.imp.common.core.domain.entity.SysUser;
import com.qmqb.imp.common.core.domain.model.LoginUser;
import com.qmqb.imp.common.enums.*;
import com.qmqb.imp.common.helper.LoginHelper;
import com.qmqb.imp.system.domain.bo.performance.PerformanceFeedbackBo;
import com.qmqb.imp.system.domain.performance.PerformanceFeedback;
import com.qmqb.imp.system.domain.performance.PerformanceFeedbackMain;
import com.qmqb.imp.system.domain.vo.performance.PerformanceFeedbackVo;
import com.qmqb.imp.system.mapper.performance.PerformanceFeedbackMainMapper;
import com.qmqb.imp.system.mapper.performance.PerformanceFeedbackMapper;
import com.qmqb.imp.system.service.ISysUserService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 绩效反馈校验工具类
 *
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class PerformanceFeedbackValidationService {

    /**
     * 绩效等级每月提交限制配置
     */
    private static final Map<String, Integer> LEVEL_MONTHLY_LIMIT = new HashMap<>();

    static {
        // S级：组长每月最多填一次and项目经理每月最多填一次
        LEVEL_MONTHLY_LIMIT.put(ScoreLevelEnum.SCORE_S.getCode(), 1);
        // A级：组长每月最多填一次and项目经理每月最多填一次
        LEVEL_MONTHLY_LIMIT.put(ScoreLevelEnum.SCORE_A.getCode(), 1);
        // B级：不需要填
        LEVEL_MONTHLY_LIMIT.put(ScoreLevelEnum.SCORE_B.getCode(), 0);
        // C级：组长每月最多填3次and项目经理每月最多填3次
        LEVEL_MONTHLY_LIMIT.put(ScoreLevelEnum.SCORE_C.getCode(), 3);
        // D级：组长每月最多填3次and项目经理每月最多填3次
        LEVEL_MONTHLY_LIMIT.put(ScoreLevelEnum.SCORE_D.getCode(), 3);
    }

    private final PerformanceFeedbackMainMapper performanceFeedbackMainMapper;
    private final PerformanceFeedbackMapper performanceFeedbackMapper;
    private final ISysUserService sysUserService;

    /**
     * 校验新增绩效反馈数据
     *
     * @param feedbackList 反馈列表
     * @param submitter 提交人
     * @param year 年份
     * @param month 月份
     * @param eventEndTime 事件结束时间（从主表获取）
     */
    public void validateNewPerformanceFeedback(List<PerformanceFeedback> feedbackList, String submitter, Integer year, Integer month, Date eventEndTime) {
        // 基础数据校验
        if (feedbackList == null || feedbackList.isEmpty()) {
            return;
        }

        // 1. 校验事件结束时间
        validateEventEndTime(eventEndTime);

        // 技术总监不受限制
        if (LoginHelper.isJszxAdmin()) {
            return;
        }

        boolean hasError = false;
        List<List<String>> errorLists = new ArrayList<>();
        // 2. 直接校验每个反馈项的个人等级提交限制
        for (PerformanceFeedback feedback : feedbackList) {
            // 获取该等级的提交限制
            Integer levelLimit = LEVEL_MONTHLY_LIMIT.get(feedback.getRecommendedLevel());
            if (levelLimit == null || levelLimit == 0) {
                return; // B级不需要校验
            }

            // 查询该提交人本月对该被提交人该二级指标该等级已提交的记录数量
            int existingCount = getSubmitterCountForEmployeeLevel(submitter, feedback.getNickName(), feedback.getSecondaryIndicator(), feedback.getRecommendedLevel(), year, month);

            // 检查是否超过限制（已有记录数 + 本次提交数1）
            if (existingCount + 1 > levelLimit) {
                hasError = true;
                PerformanceIndicatorEnum indicatorEnum = PerformanceIndicatorEnum.fromCode(feedback.getSecondaryIndicator());
                String indicatorName = indicatorEnum != null ? indicatorEnum.getName() : feedback.getSecondaryIndicator();
                List<String> errorList = new ArrayList<>();
                errorList.add(feedback.getNickName());
                errorList.add(String.valueOf(existingCount));
                errorList.add(indicatorName);
                errorList.add(String.valueOf(feedback.getRecommendedLevel()));
                errorList.add(String.valueOf(levelLimit));
                errorLists.add(errorList);
            }
        }
        if (hasError && CollUtil.isNotEmpty(errorLists)) {
            StringBuilder errorMsg = new StringBuilder();
            for (List<String> errorList : errorLists) {
                errorMsg.append(String.format("您本月已给员工[%s]提交%s条[%s-%s等级]绩效反馈记录，每月最多%s条，不允许超过限制;",
                    errorList.get(0), errorList.get(1), errorList.get(2), errorList.get(3), errorList.get(4)));
            }
            throw new ServiceException(errorMsg.toString());
        }
    }

    /**
     * 校验项管审核
     *
     * @param mainList 主表列表
     */
    public void validateProjectManagerAudit(List<PerformanceFeedbackMain> mainList,String status,Boolean isFinalAudit) {
        for (PerformanceFeedbackMain main : mainList) {
            // 拒绝状态不需要校验
            if (PerformanceFeedbackAuditStatusEnum.REJECTED.getCode().equals(status)) {
                continue;
            }
            // 权限控制逻辑
            LoginUser loginUser = LoginHelper.getLoginUser();
            if (loginUser == null) {
                throw new ServiceException("当前用户未登录");
            }
            SysUser currentUser = sysUserService.selectUserById(loginUser.getUserId());
            //去除项目经理、技术总监的审核条数限制
            if (currentUser != null && currentUser.getRoles() != null) {
                return;
            }
            if (currentUser.isJszxAdmin() || currentUser.isProjectManager()) {
                return;
            }
            String submitter = main.getSubmitter();
            Integer year = main.getYear();
            Integer month = main.getMonth();

            // 获取该提交人本月已审核通过的数据
            PerformanceFeedbackBo bo = new PerformanceFeedbackBo();
            bo.setSubmitter(submitter);
            bo.setYear(year);
            bo.setMonth(month);
            bo.setDataSource(PerformanceFeedbackDataSourceEnum.MANUAL_ADD.getCode());
            bo.setSecondaryIndicator(main.getSecondaryIndicator());
            if (isFinalAudit) {
                // 如果是最终审核，查询所有已审核数据
                bo.setFinalAudit(PerformanceFeedbackAuditStatusEnum.APPROVED.getCode());
            } else {
                // 如果是项管审核，查询所有已审核数据
                bo.setProjectManagerAuditStatus(PerformanceFeedbackAuditStatusEnum.APPROVED.getCode());
            }
            List<PerformanceFeedbackVo> approvedList = performanceFeedbackMapper.selectVoJoinMainList(bo);

            // 按等级分组统计
            Map<String, Long> levelCountMap = approvedList.stream()
                .collect(Collectors.groupingBy(
                    PerformanceFeedbackVo::getRecommendedLevel,
                    Collectors.counting()
                ));

            PerformanceFeedbackBo feedbackBo = new PerformanceFeedbackBo();
            feedbackBo.setMainFeedbackId(main.getId());
            List<PerformanceFeedbackVo> currentAuditVo = performanceFeedbackMapper.selectVoJoinMainList(feedbackBo);

            // 合并levelCountMap和currentAuditVo，
            for (PerformanceFeedbackVo feedbackVo : currentAuditVo) {
                String level = feedbackVo.getRecommendedLevel();
                levelCountMap.put(level, levelCountMap.getOrDefault(level, 0L) + 1);
            }

            // 已审核数据+本次应审核数据，校验各等级是否超过限制
            for (Map.Entry<String, Long> entry : levelCountMap.entrySet()) {
                String level = entry.getKey();
                Long count = entry.getValue();
                Integer limit = LEVEL_MONTHLY_LIMIT.get(level);

                if (limit != null && count > limit) {
                    String indicatorDesc = main.getSecondaryIndicator() != null ? "(" + main.getSecondaryIndicator() + ")" : "";
                    throw new ServiceException(String.format("%s本月提交%s%s绩效数据已审核通过数量超过限定数量", submitter, level, indicatorDesc));
                }
            }
        }
    }

    /**
     * 校验最终审核
     *
     * @param mainList 主表列表
     */
    public void validateFinalAudit(List<PerformanceFeedbackMain> mainList,String status) {
        // 最终审核的校验逻辑与项管审核相同
        validateProjectManagerAudit(mainList,status,true);
    }

    /**
     * 校验事件结束时间是否为当月
     *
     * @param eventEndTime 事件结束时间
     */
    private void validateEventEndTime(Date eventEndTime) {
        if (eventEndTime == null) {
            return;
        }

        LocalDate now = LocalDate.now();
        int currentYear = now.getYear();
        int currentMonth = now.getMonthValue();

        LocalDate eventEndDate = eventEndTime.toInstant()
            .atZone(ZoneId.systemDefault()).toLocalDate();

        if (eventEndDate.getYear() != currentYear || eventEndDate.getMonthValue() != currentMonth) {
            throw new ServiceException("仅允许提交结束时间为当月的数据，请修改事件发生时间");
        }
    }


    /**
     * 校验个人等级提交限制
     * 约束一个人每月每个指标针对每个同事，仅能提交指定次数的同一等级
     *
     * @param submitter 提交人
     * @param nickName 被提交人昵称
     * @param secondaryIndicator 二级指标
     * @param level 等级
     * @param year 年份
     * @param month 月份
     */
    private void validateIndividualLevelSubmissionLimit(String submitter, String nickName, String secondaryIndicator, String level, Integer year, Integer month) {

    }

    /**
     * 获取指定提交人已为某员工某二级指标某等级提交的记录数量
     *
     * @param submitter 提交人
     * @param nickName 被提交人昵称
     * @param secondaryIndicator 二级指标
     * @param level 等级
     * @param year 年份
     * @param month 月份
     * @return 该提交人已提交的记录数量
     */
    private int getSubmitterCountForEmployeeLevel(String submitter, String nickName, String secondaryIndicator, String level,
                                                 Integer year, Integer month) {
        // 构建查询条件
        PerformanceFeedbackBo bo = new PerformanceFeedbackBo();
        bo.setSubmitter(submitter);
        bo.setNickName(nickName);
        bo.setSecondaryIndicator(secondaryIndicator);
        bo.setRecommendedLevel(level);
        bo.setYear(year);
        bo.setMonth(month);
        bo.setDataSource(PerformanceFeedbackDataSourceEnum.MANUAL_ADD.getCode());

        // 查询该提交人对该员工在指定年月、二级指标和等级下的绩效反馈记录
        List<PerformanceFeedbackVo> existingFeedbacks = performanceFeedbackMapper.selectVoJoinMainList(bo);

        // 返回记录数量
        return existingFeedbacks.size();
    }


}
