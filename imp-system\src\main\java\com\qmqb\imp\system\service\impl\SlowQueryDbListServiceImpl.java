package com.qmqb.imp.system.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hzed.structure.common.exception.ServiceException;
import com.qmqb.imp.common.constant.UserConstants;
import com.qmqb.imp.common.core.domain.PageQuery;
import com.qmqb.imp.common.core.domain.entity.SysDictData;
import com.qmqb.imp.common.core.domain.entity.SysUser;
import com.qmqb.imp.common.core.domain.model.LoginUser;
import com.qmqb.imp.common.core.page.TableDataInfo;
import com.qmqb.imp.common.enums.PersonTypeEnum;
import com.qmqb.imp.common.helper.LoginHelper;
import com.qmqb.imp.system.domain.DataBasePermission;
import com.qmqb.imp.system.domain.SlowMonthStats;
import com.qmqb.imp.system.domain.SlowQueryStats;
import com.qmqb.imp.system.domain.bo.*;
import com.qmqb.imp.system.domain.enums.SlowQueryProcessStatusEnum;
import com.qmqb.imp.system.domain.vo.SlowMonthStatsVo;
import com.qmqb.imp.system.domain.vo.SlowQueryDbListVO;
import com.qmqb.imp.system.domain.vo.SlowQueryDetailVO;
import com.qmqb.imp.system.domain.vo.SlowQueryLogVo;
import com.qmqb.imp.system.mapper.*;
import com.qmqb.imp.system.service.ISlowQueryDbListService;
import com.qmqb.imp.system.service.ISysDictDataService;
import com.qmqb.imp.system.service.ISysUserService;
import com.qmqb.imp.system.service.ZentaoApiService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 慢sql列表Service接口
 * @date 2025/5/8 17:43
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class SlowQueryDbListServiceImpl implements ISlowQueryDbListService {

    private final SlowQueryDbListMapper slowQueryDbListMapper;

    private final SlowQueryInfoMapper slowQueryInfoMapper;

    private final SlowQueryLogMapper slowQueryLogMapper;

    private final SlowQueryProcessInfoMapper slowQueryProcessInfoMapper;

    private final SlowMonthStatsMapper slowMonthStatsMapper;

    private final ISysUserService sysUserService;

    private final ZentaoApiService zentaoApiService;

    private final DataBasePermissionMapper dataBasePermissionMapper;

    private final ISysDictDataService sysDictDataService;

    private final SlowQueryStatsMapper slowQueryStatsMapper;



    @Override
    public List<SlowQueryDbListVO> list() {
        return slowQueryDbListMapper.selectList()
            .stream().map(SlowQueryDbListVO::of).collect(Collectors.toList());
    }

    @Override
    public TableDataInfo<SlowMonthStatsVo> slowSqlPage(SlowMonthStatsBo bo, PageQuery pageQuery) {

        bo.setIdList(null);
        bo.setDbList(null);
        bo.setProcesserId(null);
        if (pageQuery.getOrderByColumn() == null) {
            pageQuery.setOrderByColumn("status,assign_time");
            pageQuery.setIsAsc("asc,desc");
        }
        // 权限控制逻辑
        LoginUser loginUser = LoginHelper.getLoginUser();
        if (loginUser == null) {
            throw new ServiceException("当前用户未登录");
        }
        SysUser currentUser = sysUserService.selectUserById(loginUser.getUserId());
        if (currentUser == null) {
            throw new ServiceException("用户信息不存在");
        }
        List<DataBasePermission> dataBasePermissionList = dataBasePermissionMapper.selectList();
        if (currentUser.isAdmin() || currentUser.isJszxAdmin() || currentUser.isProjectManager() || UserConstants.DBA_TEAM.equals(currentUser.getDept().getDeptId())) {
            bo.setDbList(null);
        }else if (currentUser.getRoles().stream().anyMatch(role -> role.getRoleId().equals(PersonTypeEnum.TECHNICAL_MANAGER.getRoleId()))) {
            List<String> dbNameList = dataBasePermissionList.stream().filter(dataBasePermission -> dataBasePermission.getGroupIds()
                .contains(String.valueOf(currentUser.getDeptId()))).map(DataBasePermission::getDbName).collect(Collectors.toList());
            if (CollectionUtil.isEmpty(dbNameList)) {
                return TableDataInfo.build();
            }
            bo.setDbList(dbNameList);
        }else {
            bo.setProcesserId(currentUser.getUserId());
        }
        Page<SlowMonthStatsVo> result = slowMonthStatsMapper.slowSqlPage(pageQuery.build(), bo);
        result.getRecords().forEach(record -> {
            if (record.getStatus() == null) {
                record.setStatus(SlowQueryProcessStatusEnum.NOT_ASSIGN.getCode());
            }
            if (record.getAvgQueryTime() != null) {
                record.setAvgQueryTime(record.getAvgQueryTime() / 1000);
            }
            if (record.getTotalSumTime() != null) {
                record.setTotalSumTime(record.getTotalSumTime() / 1000);
            }
        });
        return TableDataInfo.build(result);
    }

    @Override
    public TableDataInfo<SlowQueryDetailVO> slowSqlDetailPage(SlowQueryDetailBO bo, PageQuery pageQuery) {
        Page<SlowQueryDetailVO> result = slowQueryLogMapper.slowSqlDetailPage(pageQuery.build(), bo.getSqlHash());
        result.getRecords().forEach(record -> {
            if (record.getQueryTimeMs() != null) {
                record.setQueryTimeMs(record.getQueryTimeMs() / 1000);
            }
        });
        return TableDataInfo.build(result);
    }


    @Override
    public void process(SlowQueryProcessBo bo) {
        // 获取当前登录用户信息
        LoginUser loginUser = LoginHelper.getLoginUser();
        if (loginUser == null) {
            throw new ServiceException("当前用户未登录");
        }
        SysUser currentUser = sysUserService.selectUserById(loginUser.getUserId());
        if (currentUser == null) {
            throw new ServiceException("用户信息不存在");
        }
        // 处理：除技术总监、项目经理、测试人员外所有人
        if (currentUser.isJszxAdmin() || currentUser.isProjectManager() || currentUser.getRoles().stream()
            .anyMatch(role -> role.getRoleId().equals(PersonTypeEnum.TESTER.getRoleId()))) {
            throw new ServiceException("您没有权限执行处理操作");
        }
        List<Long> processerIdList = slowMonthStatsMapper.selectList(new LambdaQueryWrapper<SlowMonthStats>()
            .in(SlowMonthStats::getId, bo.getIdList())).stream().map(SlowMonthStats::getProcesserId).collect(Collectors.toList());
        if (processerIdList.stream().anyMatch(processerId -> !processerId.equals(loginUser.getUserId()))) {
            throw new ServiceException("您没有权限执行处理操作");
        }
        slowMonthStatsMapper.update(null, new LambdaUpdateWrapper<SlowMonthStats>()
            .set(SlowMonthStats::getRemark, bo.getRemark())
            .set(SlowMonthStats::getProcessTime, LocalDateTime.now())
            .set(SlowMonthStats::getStatus, bo.getStatus())
            .in(SlowMonthStats::getId, bo.getIdList())
            .eq(SlowMonthStats::getProcesserId, loginUser.getUserId()));
        syncStat(bo.getIdList(),bo.getStatus());
    }

    @Override
    public void assign(SlowQueryAssignBo bo) {
        // 获取当前登录用户信息
        LoginUser loginUser = LoginHelper.getLoginUser();
        if (loginUser == null) {
            throw new ServiceException("当前用户未登录");
        }
        SysUser currentUser = sysUserService.selectUserById(loginUser.getUserId());
        if (currentUser == null) {
            throw new ServiceException("用户信息不存在");
        }
        if (currentUser.getRoles().stream().noneMatch(role -> role.getRoleId().equals(PersonTypeEnum.TECHNICAL_MANAGER.getRoleId()))) {
            throw new ServiceException("仅限技术经理可以指派");
        }
        SysUser memberUser = sysUserService.selectUserById(bo.getMemberId());
        slowMonthStatsMapper.update(null, new LambdaUpdateWrapper<SlowMonthStats>()
            .set(SlowMonthStats::getAssignerId, currentUser.getUserId())
            .set(SlowMonthStats::getAssigner, currentUser.getNickName())
            .set(SlowMonthStats::getProcesserId, memberUser.getUserId())
            .set(SlowMonthStats::getProcesser, memberUser.getNickName())
            .set(SlowMonthStats::getAssignTime, LocalDateTime.now())
            .set(SlowMonthStats::getStatus, SlowQueryProcessStatusEnum.ASSIGN_NOT_PROCESS.getCode())
            .set(SlowMonthStats::getRemark, null)
            .set(SlowMonthStats::getProcessTime, null)
            .in(SlowMonthStats::getId, bo.getIdList()));
        createZentaoTaskForCurrentAssignment(bo.getIdList(), bo.getMemberId());
        syncStat(bo.getIdList(),SlowQueryProcessStatusEnum.ASSIGN_NOT_PROCESS.getCode());
    }

    /**
     * 同步统计状态
     *
     * @param idList
     * @param status
     */
    private void syncStat(List<Long> idList, String status) {
        if (CollectionUtil.isEmpty(idList)) {
            return;
        }
        List<String> sqlHashList = slowMonthStatsMapper.selectList(new LambdaQueryWrapper<SlowMonthStats>().select(SlowMonthStats::getSqlHash)
            .in(SlowMonthStats::getId, idList)).stream().map(SlowMonthStats::getSqlHash).filter(Objects::nonNull).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(sqlHashList)) {
            return;
        }
        slowQueryStatsMapper.update(null, new LambdaUpdateWrapper<SlowQueryStats>()
            .set(SlowQueryStats::getStatus, status).in(SlowQueryStats::getSqlHash, sqlHashList));

    }

    @Override
    public List<SysUser> processerList(ProcesserQueryParamsBO bo) {
        List<Collection<String>> list = dataBasePermissionMapper.selectList(new LambdaQueryWrapper<DataBasePermission>().in(DataBasePermission::getDbName, bo.getDbNameList()))
            .stream().map(dataBasePermission -> Arrays.asList(dataBasePermission.getMemberIds().split(","))).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(list)) {
            throw new ServiceException("无处理人可选！");
        }
        Collection<String> processerIds = list.stream().reduce(list.get(0), (a, b) -> CollectionUtil.intersection(a, b));
        if (CollectionUtil.isEmpty(processerIds)) {
            throw new ServiceException("无处理人可选！");
        }
        return sysUserService.selectUserByIds(processerIds);
    }

    /**
     * 为当前指派操作创建禅道任务
     *
     * @param ids          当前指派的慢sql的id列表
     * @param handleUserId 处理人ID
     */
    private void createZentaoTaskForCurrentAssignment(List<Long> ids, Long handleUserId) {
        try {
            // 根据用户ID获取用户信息
            SysUser handleUser = sysUserService.selectUserById(handleUserId);
            if (handleUser == null) {
                log.error("无法找到用户ID为{}的用户信息，禅道任务创建失败", handleUserId);
                throw new ServiceException("处理人用户信息不存在");
            }
            SlowMonthStatsBo bo = new SlowMonthStatsBo();
            bo.setIdList(ids);
            List<SlowMonthStatsVo> list = slowMonthStatsMapper.slowSqlPage(new PageQuery().build(), bo).getRecords();
            SysDictData sysDictData = new SysDictData();
            sysDictData.setDictType("slow_query_rule");
            Map<String, String> dictMap = sysDictDataService.getList(sysDictData).stream().collect(Collectors.toMap(SysDictData::getDictValue, SysDictData::getDictLabel));
            list.forEach(record -> {
                if (record.getStatus() == null) {
                    record.setStatus(SlowQueryProcessStatusEnum.NOT_ASSIGN.getCode());
                }
                if (record.getAvgQueryTime() != null) {
                    record.setAvgQueryTime(record.getAvgQueryTime() / 1000);
                }
                if (record.getTotalSumTime() != null) {
                    record.setTotalSumTime(record.getTotalSumTime() / 1000);
                }
                record.setSlowRule(dictMap.getOrDefault(record.getSlowRule(), ""));
            });
            if (list.isEmpty()) {
                log.info("没有找到指派的sql信息，不创建禅道任务");
                return;
            }
            // 生成任务名称
            String taskName = "慢SQL修复任务" + System.currentTimeMillis();
            // 生成任务描述
            String description = generateTaskDescription(list);
            // 创建禅道任务（使用用户名）
            zentaoApiService.createTask(taskName, handleUser.getZtUserName(), description);
            log.info("为用户{}创建禅道任务成功，任务名称：{}", handleUser.getNickName(), taskName);
        } catch (Exception e) {
            log.error("为用户ID{}创建禅道任务失败", handleUserId, e);
            throw e;
        }
    }


    /**
     * 生成禅道任务描述
     *
     * @param list 指派的文件列表
     * @return 任务描述
     */
    private String generateTaskDescription(List<SlowMonthStatsVo> list) {
        StringBuilder description = new StringBuilder();
        description.append("<p>您被指派对以下慢SQL问题进行处理，具体问题明细请到绩效管理系统-【工作管理-生产慢SQL管理】查看详情。修复完以下问题后请到慢SQL管理页面确认处理完成。</p>");

        // HTML表格开始
        description.append("<table border='1' style='border-collapse: collapse; width: 100%;'>");
        description.append("<thead>");
        description.append("<tr>");
        description.append("<th style='padding: 8px; text-align: left;'>库名</th>");
        description.append("<th style='padding: 8px; text-align: left;'>IP</th>");
        description.append("<th style='padding: 8px; text-align: center;'>严重程度</th>");
        description.append("<th style='padding: 8px; text-align: center;'>涉及规则</th>");
        description.append("<th style='padding: 8px; text-align: center;'>SQL文本</th>");
        description.append("<th style='padding: 8px; text-align: center;'>处理人</th>");
        description.append("<th style='padding: 8px; text-align: center;'>指派人</th>");
        description.append("<th style='padding: 8px; text-align: center;'>指派时间</th>");
        description.append("</tr>");
        description.append("</thead>");
        description.append("<tbody>");

        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        // 添加每个文件的信息
        for (SlowMonthStatsVo info : list) {
            description.append("<tr>");
            description.append("<td style='padding: 8px;'>");
            description.append(info.getDbName());
            description.append("</td>");
            description.append("<td style='padding: 8px;'>");
            description.append(info.getIp());
            description.append("</td>");
            description.append("<td style='padding: 8px; text-align: center;'>");
            description.append(info.getWarnLevel());
            description.append("</td>");
            description.append("<td style='padding: 8px; text-align: center;'>");
            description.append(info.getSlowRule());
            description.append("</td>");
            description.append("<td style='padding: 8px; text-align: center;'>");
            description.append(info.getSqlText());
            description.append("</td>");
            description.append("<td style='padding: 8px; text-align: center;'>");
            description.append(info.getProcesser());
            description.append("</td>");
            description.append("<td style='padding: 8px; text-align: center;'>");
            description.append(info.getAssigner());
            description.append("</td>");
            description.append("<td style='padding: 8px; text-align: center;'>");
            description.append(dateFormat.format(info.getAssignTime()));
            description.append("</td>");
            description.append("</tr>");
        }

        // HTML表格结束
        description.append("</tbody>");
        description.append("</table>");

        return description.toString();
    }

}
