package com.qmqb.imp.system.service.indicator;


import com.qmqb.imp.system.domain.bo.performance.PerformIndicatorResultBo;
import com.qmqb.imp.system.domain.vo.PerformIndicatorResultVo;
import com.qmqb.imp.common.core.page.TableDataInfo;
import com.qmqb.imp.common.core.domain.PageQuery;
import com.qmqb.imp.system.domain.vo.performance.IndicatorCategoryVo;

import java.util.List;

/**
 * 绩效指标原因Service接口
 *
 * <AUTHOR>
 * @date 2025-07-01
 */
public interface IPerformIndicatorResultService {


    /**
     * 查询绩效指标原因列表
     * @param bo
     * @param pageQuery
     * @return
     */
    TableDataInfo<PerformIndicatorResultVo> queryPageList(PerformIndicatorResultBo bo, PageQuery pageQuery);

    /**
     * 查询绩效指标原因列表
     * @param bo
     * @return
     */
    List<PerformIndicatorResultVo> queryList(PerformIndicatorResultBo bo);

    /**
     * 新增绩效指标原因
     * @param bo
     * @return
     */
    Boolean insertByBo(PerformIndicatorResultBo bo);

    /**
     * 修改绩效指标原因
     * @param bo
     * @return
     */
    Boolean updateByBo(PerformIndicatorResultBo bo);

    /**
     * 根据id删除绩效指标原因
     * @param resultsIds
     */
    void deleteByIds(Long[] resultsIds);

    /**
     * 获取角色的一类二类指标下拉数据
     * @param roleId
     * @param indicatorsByRole
     * @return
     */
    List<IndicatorCategoryVo> getRoleIndicator(Long roleId, List<String> indicatorsByRole);
}
